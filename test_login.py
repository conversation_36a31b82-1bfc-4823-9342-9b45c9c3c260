#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录功能测试脚本
"""

import requests
import base64
import json

def test_login():
    """测试登录功能"""
    base_url = "http://localhost:18084"

    print("🔐 开始测试登录功能...")

    # 1. 获取验证码
    print("\n1️⃣ 获取验证码...")
    try:
        captcha_response = requests.get(f"{base_url}/captcha")
        if captcha_response.status_code == 200:
            captcha_data = captcha_response.json()
            captcha_key = captcha_data['key']
            print(f"✅ 验证码获取成功，Key: {captcha_key}")
        else:
            print(f"❌ 验证码获取失败，状态码: {captcha_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 验证码获取异常: {e}")
        return False

    # 2. 测试不同的密码
    print("\n2️⃣ 测试不同的密码...")
    username = "admin"
    test_passwords = ["admin123", "admin", "123456", "password"]

    for password in test_passwords:
        print(f"\n🔑 测试密码: {password}")

        # 创建Basic Auth头
        credentials = f"{username}:{password}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        auth_header = f"Basic {encoded_credentials}"

        # 登录请求
        login_data = {
            "key": captcha_key,
            "captcha": "1234"  # 使用简单的测试验证码
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": auth_header
        }

        try:
            login_response = requests.post(
                f"{base_url}/login",
                json=login_data,
                headers=headers
            )

            print(f"   状态码: {login_response.status_code}")

            if login_response.status_code == 200:
                response_data = login_response.json()
                print(f"   ✅ 登录成功!")
                print(f"   响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")

                # 检查JWT token
                auth_header_response = login_response.headers.get('Authorization')
                if auth_header_response:
                    print(f"   🔑 JWT Token: {auth_header_response}")
                else:
                    print("   ⚠️ 未找到JWT Token")

                return True
            elif login_response.status_code == 400:
                try:
                    error_data = login_response.json()
                    print(f"   ❌ 400错误: {error_data.get('message', '未知错误')}")
                except:
                    print(f"   ❌ 400错误: {login_response.text}")
            elif login_response.status_code == 401:
                print("   ❌ 401错误: 认证失败")
            else:
                print(f"   ❌ 其他错误，状态码: {login_response.status_code}")
                try:
                    error_data = login_response.json()
                    print(f"   错误信息: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
                except:
                    print(f"   响应内容: {login_response.text}")

        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

    return False

def test_password_verification():
    """测试密码验证"""
    print("\n🔍 验证admin用户密码...")
    
    # 这里我们可以测试不同的密码
    test_passwords = ["admin123", "admin", "123456", "password"]
    
    for pwd in test_passwords:
        print(f"\n测试密码: {pwd}")
        # 这里只是演示，实际测试需要验证码
        credentials = f"admin:{pwd}"
        encoded = base64.b64encode(credentials.encode()).decode()
        print(f"Basic Auth: Basic {encoded}")

if __name__ == "__main__":
    print("="*60)
    print("电力线路设备缺陷识别系统 - 登录功能测试")
    print("="*60)
    
    # 测试登录
    success = test_login()
    
    # 测试密码验证
    test_password_verification()
    
    print("\n" + "="*60)
    if success:
        print("🎉 测试完成！登录功能基本正常")
    else:
        print("⚠️ 测试完成！发现问题需要修复")
    print("="*60)
