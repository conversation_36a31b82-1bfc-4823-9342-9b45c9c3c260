import { Notification } from "@douyinfe/semi-ui";
import axiosInstance from "./axiosInstance";
import { errorNotification, successNotification } from "./notification";
import { myProfile } from "./profileOperate";

/**
 * async GET 请求 /captcha
 */
export async function getCaptcha() {
  try {
    const response = await axiosInstance.get("/captcha");
    if (response.status === 200) {
      return response.data;
    }
  } catch (error) {
    Notification.error({
      title: "获取验证码失败",
      content: "请求服务器出错",
    });
  }
}

export async function login(
  userName,
  password,
  captchaKey,
  captcha,
  setIsLogin,
  setRole,
  setUserName,
  setName,
  setRoleDisplayName
) {
  const failLogin = () => {
    localStorage.removeItem("authorization");
    setIsLogin(false);
  };

  try {
    console.log("Attempting login with:", { userName, captcha<PERSON>ey, captcha });
    const authHeader = `Basic ${btoa(`${userName}:${password}`)}`;
    console.log("Auth header created (not showing password)");

    const response = await axiosInstance.post(
      "/login",
      {
        key: captchaKey,
        captcha,
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: authHeader,
        },
      }
    );

    console.log("Login response:", response);

    if (response.status === 200) {
      const { roles } = response.data;
      // 获取响应头
      const token = response.headers.authorization;
      const bearerToken = token.split(" ")[1];
      localStorage.setItem("authorization", bearerToken);
      axiosInstance.defaults.headers.common["Authorization"] = token;
      const profileData = await myProfile();
      if (profileData) {
        setIsLogin(true);
        setRole(roles[0]);
        setUserName(profileData.name);
        setName(profileData.profile.name);
        setRoleDisplayName(profileData.roleDisplayName);
      }
      successNotification(response);
    }
  } catch (error) {
    console.log("Login error details:", error);
    failLogin();
    Notification.error({
      title: "登录失败",
      content: "用户名、密码或验证码错误",
    });
  }
}

export async function regUser(values) {
  try {
    const res = await axiosInstance({
      url: "/reg",
      method: "post",
      data: {
        userName: values.username,
        password: values.password,
        inviteCode: values.inviteCode,
        name: values.name,
        sex: values.sex,
        phone: values.phone,
      },
    });
    if (res.status === 200) {
      successNotification(res);
      return true;
    }
    return false;
  } catch (error) {
    errorNotification(error);
    return false;
  }
}
