#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密码验证脚本
"""

import bcrypt

def verify_password():
    """验证密码"""
    # 从数据库中获取的密码哈希
    stored_hash = "$2a$10$rJf5Ufgr4mpGRnT9nGKL0.pu.vOAOh/i0KV3BM5/TZgjjK.2qzAVm"
    
    # 测试不同的密码
    test_passwords = ["admin123", "admin", "123456", "password", "test"]
    
    print("🔐 验证admin用户密码...")
    print(f"存储的哈希: {stored_hash}")
    print("-" * 60)
    
    for password in test_passwords:
        try:
            # BCrypt验证
            is_valid = bcrypt.checkpw(password.encode('utf-8'), stored_hash.encode('utf-8'))
            status = "✅ 正确" if is_valid else "❌ 错误"
            print(f"密码 '{password}': {status}")
            
            if is_valid:
                print(f"🎉 找到正确密码: {password}")
                return password
        except Exception as e:
            print(f"密码 '{password}': ❌ 验证异常 - {e}")
    
    print("\n⚠️ 未找到匹配的密码")
    return None

def generate_test_hash():
    """生成测试密码的哈希"""
    password = "admin123"
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    print(f"\n🔧 生成新的密码哈希:")
    print(f"密码: {password}")
    print(f"哈希: {hashed.decode('utf-8')}")

if __name__ == "__main__":
    print("="*60)
    print("密码验证工具")
    print("="*60)
    
    try:
        correct_password = verify_password()
        generate_test_hash()
    except ImportError:
        print("❌ 需要安装bcrypt库: pip install bcrypt")
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
