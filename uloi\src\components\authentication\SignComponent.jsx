import { Button, Form } from "@douyinfe/semi-ui";
import { EuiFlexGroup, EuiFlexItem, EuiImage } from "@elastic/eui";
import { useState, useEffect } from "react";
import { getCaptcha, login } from "../../operate/authOperate";
import loginAction from "../../action/loginAction";
import profileBasic from "../../action/profileBasic";

const SignComponent = () => {
  // 验证码图片
  const [captchaImage, setCaptchaImage] = useState(null);
  // 验证码键
  const [captchaKey, setCaptchaKey] = useState(null);

  const handleFetchCaptcha = async () => {
    try {
      const { key, image } = await getCaptcha();
      setCaptchaKey(key);
      setCaptchaImage(image);
    } catch (error) {}
  };

  const { setIsLogin } = loginAction();
  const { setRole, setUserName, setName, setRoleDisplayName } = profileBasic();

  // 组件加载时自动获取验证码
  useEffect(() => {
    handleFetchCaptcha();
  }, []);

  const handleSubmit = async (values) => {
    await login(
      values.username,
      values.password,
      captchaKey,
      values.captcha,
      setIsLogin,
      setRole,
      setUserName,
      setName,
      setRoleDisplayName
    );
  };

  return (
    <Form
      onSubmit={(values) => {
        handleSubmit(values);
      }}
    >
      <Form.Input field="username" label="用户名" />
      <Form.Input field="password" mode="password" label="密码" />
      <Form.Input field="captcha" label="验证码" />
      <EuiImage hasShadow alt="验证码" src={captchaImage} />
      <EuiFlexGroup>
        <EuiFlexItem grow={false}>
          <Button type="secondary" theme="solid" onClick={handleFetchCaptcha}>
            刷新验证码
          </Button>
        </EuiFlexItem>
        <EuiFlexItem grow={false}>
          <Button htmlType="submit" theme="solid">
            登录
          </Button>
        </EuiFlexItem>
      </EuiFlexGroup>
    </Form>
  );
};

export default SignComponent;
