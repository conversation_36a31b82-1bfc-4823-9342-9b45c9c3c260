#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码测试脚本
"""

import requests
import base64
import json
import redis

def test_captcha_and_login():
    """测试验证码和登录功能"""
    base_url = "http://localhost:18084"
    
    print("🔐 开始测试验证码和登录功能...")
    
    # 连接Redis
    try:
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        r.ping()
        print("✅ Redis连接成功")
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False
    
    # 1. 获取验证码
    print("\n1️⃣ 获取验证码...")
    try:
        captcha_response = requests.get(f"{base_url}/captcha")
        if captcha_response.status_code == 200:
            captcha_data = captcha_response.json()
            captcha_key = captcha_data['key']
            captcha_image = captcha_data['image']
            print(f"✅ 验证码获取成功，Key: {captcha_key}")
            print(f"验证码图片长度: {len(captcha_image)} 字符")
            
            # 从Redis中获取验证码的实际值
            actual_captcha = r.get(captcha_key)
            if actual_captcha:
                print(f"🔑 从Redis获取的验证码值: {actual_captcha}")
            else:
                print("❌ 无法从Redis获取验证码值")
                return False
        else:
            print(f"❌ 验证码获取失败，状态码: {captcha_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 验证码获取异常: {e}")
        return False
    
    # 2. 使用正确的验证码进行登录测试
    print("\n2️⃣ 使用正确的验证码测试登录...")
    username = "admin"
    password = "admin123"
    
    # 创建Basic Auth头
    credentials = f"{username}:{password}"
    encoded_credentials = base64.b64encode(credentials.encode()).decode()
    auth_header = f"Basic {encoded_credentials}"
    
    # 登录请求
    login_data = {
        "key": captcha_key,
        "captcha": actual_captcha  # 使用从Redis获取的正确验证码
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": auth_header
    }
    
    try:
        login_response = requests.post(
            f"{base_url}/login",
            json=login_data,
            headers=headers
        )
        
        print(f"登录响应状态码: {login_response.status_code}")
        print(f"登录响应头: {dict(login_response.headers)}")
        
        if login_response.status_code == 200:
            response_data = login_response.json()
            print(f"✅ 登录成功!")
            print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            # 检查JWT token
            auth_header_response = login_response.headers.get('Authorization')
            if auth_header_response:
                print(f"🔑 JWT Token: {auth_header_response}")
            else:
                print("⚠️ 未找到JWT Token")
                
            return True
        elif login_response.status_code == 400:
            try:
                error_data = login_response.json()
                print(f"❌ 400错误: {error_data.get('message', '未知错误')}")
            except:
                print(f"❌ 400错误: {login_response.text}")
        elif login_response.status_code == 401:
            print("❌ 401错误: 认证失败")
        else:
            print(f"❌ 其他错误，状态码: {login_response.status_code}")
            try:
                error_data = login_response.json()
                print(f"错误信息: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
            except:
                print(f"响应内容: {login_response.text}")
                
    except Exception as e:
        print(f"❌ 登录请求异常: {e}")
    
    return False

def test_redis_keys():
    """测试Redis中的验证码键"""
    try:
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        keys = r.keys("*")
        print(f"\n🔍 Redis中的所有键: {keys}")
        
        for key in keys:
            value = r.get(key)
            ttl = r.ttl(key)
            print(f"键: {key}, 值: {value}, TTL: {ttl}秒")
    except Exception as e:
        print(f"❌ Redis查询异常: {e}")

if __name__ == "__main__":
    print("="*60)
    print("验证码和登录功能测试")
    print("="*60)
    
    # 测试Redis中的键
    test_redis_keys()
    
    # 测试验证码和登录
    success = test_captcha_and_login()
    
    print("\n" + "="*60)
    if success:
        print("🎉 测试完成！登录功能正常")
    else:
        print("⚠️ 测试完成！发现问题需要修复")
    print("="*60)
