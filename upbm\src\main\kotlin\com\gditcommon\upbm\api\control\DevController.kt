package com.gditcommon.upbm.api.control

import com.gditcommon.upbm.core.repository.UserRepository
import com.gditcommon.upbm.util.CommonUtil
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/dev")
class DevController(
    private val userRepository: UserRepository,
    private val passwordEncoder: PasswordEncoder,
    private val jdbcTemplate: JdbcTemplate
) {
    @GetMapping("/set-admin-password/{password}")
    fun setAdminPassword(@PathVariable password: String): Map<String, Any> {
        // 使用 BCrypt 加密密码
        val encodedPassword = passwordEncoder.encode(password)
        
        // 直接执行SQL更新密码
        val updated = jdbcTemplate.update(
            "UPDATE user SET password = ? WHERE user_name = ?",
            encodedPassword, "admin"
        )
        
        return if (updated > 0) {
            mapOf(
                "success" to true,
                "message" to "Admin password has been set to '$password'",
                "username" to "admin",
                "encodedPassword" to encodedPassword
            )
        } else {
            mapOf(
                "success" to false,
                "error" to "Failed to update admin password"
            )
        }
    }
}



