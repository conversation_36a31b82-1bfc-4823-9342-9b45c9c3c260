import { BrowserRouter, Route, Routes, Navigate } from "react-router-dom";

import AuthPage from "./pages/AuthPage";
import AdminHomePage from "./pages/home/<USER>";
import QuestListPage from "./pages/quest/QuestListPage";

import AdminNavSilder from "./components/nav/silder/AdminNavSilder";
import QuestCreatePage from "./pages/quest/QuestCreatePage";
import QuestTodoPage from "./pages/quest/QuestTodoPage";
import QuestApprovalPage from "./pages/quest/QuestApprovalPage";
import DetectAutoPage from "./pages/datect/DetectAutoPage";
import HrManagerPage from "./pages/hr/HrManagerPage";
import SettingPage from "./pages/SettingPage";
import UserHomePage from "./pages/home/<USER>";
import UserNavSilder from "./components/nav/silder/UserNavSilder";
import NoPermissionPage from "./pages/home/<USER>/NoPermissionPage";
import NoContentPage from "./pages/home/<USER>/NoContentPage";

import loginAction from "./action/loginAction";
import { useEffect } from "react";
import axiosInstance from "./operate/axiosInstance";
import { myProfile } from "./operate/profileOperate";
import profileBasic from "./action/profileBasic";

function App() {
  const { isLogin, setIsLogin } = loginAction();
  const { role, setRole, setUserName, setName, setRoleDisplayName } =
    profileBasic();

  useEffect(() => {
    const fetchData = async () => {
      const token = localStorage.getItem("authorization");
      if (token !== null && token !== undefined) {
        axiosInstance.defaults.headers.common[
          "Authorization"
        ] = `Bearer ${token}`;
        const profileData = await myProfile();
        if (profileData) {
          setIsLogin(true);
          setRole(profileData.role);
          setUserName(profileData.name);
          setName(profileData.profile.name);
          setRoleDisplayName(profileData.roleDisplayName);
        }
      }
    };

    fetchData();
  }, []);

  const elementRole = ({ AdminPage, UserPage, VictorPage }) => {
    if (!isLogin) {
      return <Navigate to="/auth" />;
    }

    switch (role) {
      case "ADMIN":
        return <AdminPage />;
      case "DATA_UPLOADER":
      case "WORKER":
      case "VERIFIER":
        return <UserPage />;
      case "USER":
        return <VictorPage />;
      default:
        return <Navigate to="/auth" />;
    }
  };

  return (
    <BrowserRouter>
      <Routes>
        <Route
          path="/"
          element={elementRole({
            AdminPage: () => (
              <NoContentPage silder={<AdminNavSilder select="Home" />} />
            ),
            UserPage: NoContentPage,
            VictorPage: NoPermissionPage,
          })}
        />
        <Route
          path="/auth"
          element={isLogin ? <Navigate to="/" /> : <AuthPage />}
        />
        <Route
          path="/quest/list"
          element={elementRole({
            AdminPage: () => (
              <QuestListPage silder={<AdminNavSilder select="QuestList" />} />
            ),
            UserPage: () => (
              <QuestListPage silder={<UserNavSilder select="QuestList" />} />
            ),
            VictorPage: NoPermissionPage,
          })}
        ></Route>
        <Route
          path="/quest/create"
          element={elementRole({
            AdminPage: () => (
              <QuestCreatePage
                silder={<AdminNavSilder select="CreateQuest" />}
              />
            ),
            UserPage: NoPermissionPage,
            VictorPage: NoPermissionPage,
          })}
        ></Route>
        <Route
          path="/quest/todo"
          element={elementRole({
            AdminPage: () => (
              <QuestTodoPage silder={<AdminNavSilder select="TodoList" />} />
            ),
            UserPage: () => (
              <QuestListPage silder={<UserNavSilder select="QuestList" />} />
            ),
            VictorPage: NoPermissionPage,
          })}
        />
        <Route
          path="/quest/approval"
          element={elementRole({
            AdminPage: () => (
              <QuestApprovalPage
                silder={<AdminNavSilder select="QuestApproval" />}
              />
            ),
            UserPage: NoPermissionPage,
            VictorPage: NoPermissionPage,
          })}
        />
        <Route
          path="/detect/auto"
          element={elementRole({
            AdminPage: () => (
              <DetectAutoPage silder={<AdminNavSilder select="DetectAuto" />} />
            ),
            UserPage: NoPermissionPage,
            VictorPage: NoPermissionPage,
          })}
        />
        <Route
          path="/hr/manager"
          element={elementRole({
            AdminPage: () => (
              <HrManagerPage silder={<AdminNavSilder select="Hr" />} />
            ),
            UserPage: NoPermissionPage,
            VictorPage: NoPermissionPage,
          })}
        />
        <Route
          path="/setting"
          element={elementRole({
            AdminPage: () => (
              <SettingPage silder={<AdminNavSilder select="Setting" />} />
            ),
            UserPage: NoPermissionPage,
            VictorPage: NoPermissionPage,
          })}
        />
        <Route
          path="/404"
          element={elementRole({
            AdminPage: () => (
              <NoContentPage silder={<AdminNavSilder select="404" />} />
            ),
            UserPage: NoContentPage,
            VictorPage: NoContentPage,
          })}
        />
        <Route path="*" element={<Navigate to="/404" />} />
      </Routes>
    </BrowserRouter>
  );
}

export default App;
